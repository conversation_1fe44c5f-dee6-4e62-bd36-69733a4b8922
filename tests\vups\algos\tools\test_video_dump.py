import asyncio
import time
from vups.algos.tools.video_dump import video_source_downloader
from vups.logger import logger


async def test_download_video(bvid: str = "BV1ZYavzbESS", output_filename: str = "tests/vups/data/test_video.mp4") -> None:
    """Main function to download a Bilibili video.

    Args:
        bvid: Bilibili video ID to download
        output_filename: Output file name
    """
    try:
        await video_source_downloader.download_video(bvid=bvid, output_filename=output_filename)

    except Exception as e:
        logger.error(f"Failed to download video {bvid}: {e}")
        raise

async def test_download_audio(bvid: str = "BV1NXaJzhEFD", output_filename: str = "tests/vups/data/test_audio.mp3") -> None:
    """Main function to download a Bilibili video.

    Args:
        bvid: Bilibili video ID to download
        output_filename: Output file name
    """
    try:
        await video_source_downloader.download_audio(bvid=bvid, output_filename=output_filename)

    except Exception as e:
        logger.error(f"Failed to download audio {bvid}: {e}")
        raise

async def test_download_subtitle(bvid: str = "BV1NXaJzhEFD", output_filename: str = "tests/vups/data/test_subtitle.txt") -> None:

    try:
        await video_source_downloader.download_subtitle(bvid=bvid, output_filename=output_filename, add_timestamp=True)

    except Exception as e:
        logger.error(f"Failed to download subtitle {bvid}: {e}")
        raise

async def test_parse_subtitle():
    try:
        with open("tests/vups/data/test_subtitle.txt", "r", encoding="utf-8") as f:
            input_data = f.read()

        print(repr(input_data[:200]))

        try:
            import json
            first_parse = json.loads(input_data)

            if isinstance(first_parse, str):
                json_str = first_parse.replace("'", '"')
                final_data = json.loads(json_str)
            else:
                final_data = first_parse

            data = await video_source_downloader.parse_subtitle(final_data)

        except json.JSONDecodeError as e:
            print(f"JSON解析失败: {e}")
            data = input_data

        print("返回内容:", data[0])

    except Exception as e:
        logger.error(f"Failed to parse subtitle: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(test_download_video()) # pass
    # asyncio.run(test_download_audio()) # pass
    # time
    # time1 = time.time()
    # asyncio.run(test_download_subtitle(bvid = "BV1NXaJzhEFD")) # pass
    # time2 = time.time()
    # print(f"Time cost: {time2 - time1} secs")
    # asyncio.run(test_parse_subtitle())
    # 43s video -> 8s
