# --- key_data_overview_table (keeping original for daily data) ---
create_key_data_overview_table_sql = """CREATE TABLE IF NOT EXISTS key_data_overview_table (
    id serial4 PRIMARY KEY,
    uid varchar(50) NOT NULL,
    play bigint,
    play_last bigint,
    visitor bigint,
    visitor_last bigint,
    fan bigint,
    fan_last bigint,
    like_count bigint,
    like_last bigint,
    fav bigint,
    fav_last bigint,
    coin bigint,
    coin_last bigint,
    dm bigint,
    dm_last bigint,
    comment bigint,
    comment_last bigint,
    share bigint,
    share_last bigint,
    log_date bigint,
    create_time bigint,
    update_time timestamp,
    UNIQUE (uid, log_date)
);"""

insert_key_data_overview_table_sql = """INSERT INTO key_data_overview_table (
    uid, play, play_last, visitor, visitor_last, fan, fan_last, like_count, like_last, fav, fav_last,
    coin, coin_last, dm, dm_last, comment, comment_last, share, share_last, log_date, create_time, update_time
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22
) ON CONFLICT (uid, log_date) DO UPDATE SET
    play = EXCLUDED.play,
    play_last = EXCLUDED.play_last,
    visitor = EXCLUDED.visitor,
    visitor_last = EXCLUDED.visitor_last,
    fan = EXCLUDED.fan,
    fan_last = EXCLUDED.fan_last,
    like_count = EXCLUDED.like_count,
    like_last = EXCLUDED.like_last,
    fav = EXCLUDED.fav,
    fav_last = EXCLUDED.fav_last,
    coin = EXCLUDED.coin,
    coin_last = EXCLUDED.coin_last,
    dm = EXCLUDED.dm,
    dm_last = EXCLUDED.dm_last,
    comment = EXCLUDED.comment,
    comment_last = EXCLUDED.comment_last,
    share = EXCLUDED.share,
    share_last = EXCLUDED.share_last,
    update_time = EXCLUDED.update_time;
"""

# --- Normalized weekly metrics table ---
create_weekly_metrics_table_sql = """CREATE TABLE IF NOT EXISTS weekly_metrics_table (
    id serial4 PRIMARY KEY,
    uid varchar(50) NOT NULL,
    report_type varchar(50) NOT NULL, -- 'overview', 'play_analyze', 'attention_analyze'
    metric_category varchar(50) NOT NULL, -- 'play_cnt', 'all_play', 'net_attention_cnt', etc.
    amount bigint,
    amount_pass_per bigint,
    amount_last bigint,
    amount_last_pass_per bigint,
    amount_change bigint,
    amount_med bigint,
    date_value bigint,
    tendency_list jsonb,
    additional_data jsonb, -- For any extra fields like 'tip'
    create_time bigint,
    update_time timestamp,
    UNIQUE (uid, report_type, metric_category, date_value)
);"""

# Performance indexes for the normalized table
create_weekly_metrics_indexes_sql = """
CREATE INDEX IF NOT EXISTS idx_weekly_metrics_uid_report ON weekly_metrics_table(uid, report_type);
CREATE INDEX IF NOT EXISTS idx_weekly_metrics_category ON weekly_metrics_table(metric_category);
CREATE INDEX IF NOT EXISTS idx_weekly_metrics_date ON weekly_metrics_table(date_value);
CREATE INDEX IF NOT EXISTS idx_weekly_metrics_uid_date ON weekly_metrics_table(uid, date_value);
"""

# Insert statement for normalized metrics
insert_weekly_metrics_table_sql = """INSERT INTO weekly_metrics_table (
    uid, report_type, metric_category, amount, amount_pass_per, amount_last, amount_last_pass_per,
    amount_change, amount_med, date_value, tendency_list, additional_data, create_time, update_time
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14
) ON CONFLICT (uid, report_type, metric_category, date_value) DO UPDATE SET
    amount = EXCLUDED.amount,
    amount_pass_per = EXCLUDED.amount_pass_per,
    amount_last = EXCLUDED.amount_last,
    amount_last_pass_per = EXCLUDED.amount_last_pass_per,
    amount_change = EXCLUDED.amount_change,
    amount_med = EXCLUDED.amount_med,
    tendency_list = EXCLUDED.tendency_list,
    additional_data = EXCLUDED.additional_data,
    update_time = EXCLUDED.update_time;
"""

# --- Views for backward compatibility ---
create_weekly_key_data_overview_table_sql = """CREATE OR REPLACE VIEW weekly_key_data_overview_table AS
SELECT
    ROW_NUMBER() OVER (ORDER BY uid, date_value) as id,
    uid,
    MAX(CASE WHEN metric_category = 'play_cnt' THEN amount END) as play_cnt_amount,
    MAX(CASE WHEN metric_category = 'play_cnt' THEN amount_pass_per END) as play_cnt_amount_pass_per,
    MAX(CASE WHEN metric_category = 'play_cnt' THEN amount_last END) as play_cnt_amount_last,
    MAX(CASE WHEN metric_category = 'play_cnt' THEN amount_last_pass_per END) as play_cnt_amount_last_pass_per,
    MAX(CASE WHEN metric_category = 'play_cnt' THEN amount_change END) as play_cnt_amount_change,
    MAX(CASE WHEN metric_category = 'play_cnt' THEN amount_med END) as play_cnt_amount_med,
    MAX(CASE WHEN metric_category = 'play_cnt' THEN date_value END) as play_cnt_date,
    (array_agg(CASE WHEN metric_category = 'play_cnt' THEN tendency_list END ORDER BY create_time DESC) FILTER (WHERE metric_category = 'play_cnt' AND tendency_list IS NOT NULL))[1] as play_cnt_tendency_list,
    MAX(CASE WHEN metric_category = 'interact_rate' THEN amount END) as interact_rate_amount,
    MAX(CASE WHEN metric_category = 'interact_rate' THEN amount_pass_per END) as interact_rate_amount_pass_per,
    MAX(CASE WHEN metric_category = 'interact_rate' THEN amount_last END) as interact_rate_amount_last,
    MAX(CASE WHEN metric_category = 'interact_rate' THEN amount_last_pass_per END) as interact_rate_amount_last_pass_per,
    MAX(CASE WHEN metric_category = 'interact_rate' THEN amount_change END) as interact_rate_amount_change,
    MAX(CASE WHEN metric_category = 'interact_rate' THEN amount_med END) as interact_rate_amount_med,
    MAX(CASE WHEN metric_category = 'interact_rate' THEN date_value END) as interact_rate_date,
    (array_agg(CASE WHEN metric_category = 'interact_rate' THEN tendency_list END ORDER BY create_time DESC) FILTER (WHERE metric_category = 'interact_rate' AND tendency_list IS NOT NULL))[1] as interact_rate_tendency_list,
    MAX(CASE WHEN metric_category = 'net_attention_cnt' THEN amount END) as net_attention_cnt_amount,
    MAX(CASE WHEN metric_category = 'net_attention_cnt' THEN amount_pass_per END) as net_attention_cnt_amount_pass_per,
    MAX(CASE WHEN metric_category = 'net_attention_cnt' THEN amount_last END) as net_attention_cnt_amount_last,
    MAX(CASE WHEN metric_category = 'net_attention_cnt' THEN amount_last_pass_per END) as net_attention_cnt_amount_last_pass_per,
    MAX(CASE WHEN metric_category = 'net_attention_cnt' THEN amount_change END) as net_attention_cnt_amount_change,
    MAX(CASE WHEN metric_category = 'net_attention_cnt' THEN amount_med END) as net_attention_cnt_amount_med,
    MAX(CASE WHEN metric_category = 'net_attention_cnt' THEN date_value END) as net_attention_cnt_date,
    (array_agg(CASE WHEN metric_category = 'net_attention_cnt' THEN tendency_list END ORDER BY create_time DESC) FILTER (WHERE metric_category = 'net_attention_cnt' AND tendency_list IS NOT NULL))[1] as net_attention_cnt_tendency_list,
    MAX(CASE WHEN metric_category = 'interact_fans_per' THEN amount END) as interact_fans_per_amount,
    MAX(CASE WHEN metric_category = 'interact_fans_per' THEN amount_pass_per END) as interact_fans_per_amount_pass_per,
    MAX(CASE WHEN metric_category = 'interact_fans_per' THEN amount_last END) as interact_fans_per_amount_last,
    MAX(CASE WHEN metric_category = 'interact_fans_per' THEN amount_last_pass_per END) as interact_fans_per_amount_last_pass_per,
    MAX(CASE WHEN metric_category = 'interact_fans_per' THEN amount_change END) as interact_fans_per_amount_change,
    MAX(CASE WHEN metric_category = 'interact_fans_per' THEN amount_med END) as interact_fans_per_amount_med,
    MAX(CASE WHEN metric_category = 'interact_fans_per' THEN date_value END) as interact_fans_per_date,
    (array_agg(CASE WHEN metric_category = 'interact_fans_per' THEN tendency_list END ORDER BY create_time DESC) FILTER (WHERE metric_category = 'interact_fans_per' AND tendency_list IS NOT NULL))[1] as interact_fans_per_tendency_list,
    MAX(CASE WHEN metric_category = 'avs_num' THEN amount END) as avs_num_amount,
    MAX(CASE WHEN metric_category = 'avs_num' THEN amount_pass_per END) as avs_num_amount_pass_per,
    MAX(CASE WHEN metric_category = 'avs_num' THEN amount_last END) as avs_num_amount_last,
    MAX(CASE WHEN metric_category = 'avs_num' THEN amount_last_pass_per END) as avs_num_amount_last_pass_per,
    MAX(CASE WHEN metric_category = 'avs_num' THEN amount_change END) as avs_num_amount_change,
    MAX(CASE WHEN metric_category = 'avs_num' THEN amount_med END) as avs_num_amount_med,
    MAX(CASE WHEN metric_category = 'avs_num' THEN date_value END) as avs_num_date,
    (array_agg(CASE WHEN metric_category = 'avs_num' THEN tendency_list END ORDER BY create_time DESC) FILTER (WHERE metric_category = 'avs_num' AND tendency_list IS NOT NULL))[1] as avs_num_tendency_list,
    MAX(create_time) as create_time,
    MAX(update_time) as update_time
FROM weekly_metrics_table
WHERE report_type = 'overview'
GROUP BY uid, date_value;
"""

create_weekly_play_analyze_table_sql = """CREATE OR REPLACE VIEW weekly_play_analyze_table AS
SELECT
    ROW_NUMBER() OVER (ORDER BY uid, date_value) as id,
    uid,
    MAX(CASE WHEN metric_category = 'all_play' THEN amount END) as all_play_amount,
    MAX(CASE WHEN metric_category = 'all_play' THEN amount_pass_per END) as all_play_amount_pass_per,
    MAX(CASE WHEN metric_category = 'all_play' THEN amount_last END) as all_play_amount_last,
    MAX(CASE WHEN metric_category = 'all_play' THEN amount_last_pass_per END) as all_play_amount_last_pass_per,
    MAX(CASE WHEN metric_category = 'all_play' THEN amount_change END) as all_play_amount_change,
    MAX(CASE WHEN metric_category = 'all_play' THEN amount_med END) as all_play_amount_med,
    MAX(CASE WHEN metric_category = 'all_play' THEN date_value END) as all_play_date,
    (array_agg(CASE WHEN metric_category = 'all_play' THEN tendency_list END ORDER BY create_time DESC) FILTER (WHERE metric_category = 'all_play' AND tendency_list IS NOT NULL))[1] as all_play_tendency_list,
    MAX(CASE WHEN metric_category = 'viewer_play' THEN amount END) as viewer_play_amount,
    MAX(CASE WHEN metric_category = 'viewer_play' THEN amount_pass_per END) as viewer_play_amount_pass_per,
    MAX(CASE WHEN metric_category = 'viewer_play' THEN amount_last END) as viewer_play_amount_last,
    MAX(CASE WHEN metric_category = 'viewer_play' THEN amount_last_pass_per END) as viewer_play_amount_last_pass_per,
    MAX(CASE WHEN metric_category = 'viewer_play' THEN amount_change END) as viewer_play_amount_change,
    MAX(CASE WHEN metric_category = 'viewer_play' THEN amount_med END) as viewer_play_amount_med,
    MAX(CASE WHEN metric_category = 'viewer_play' THEN date_value END) as viewer_play_date,
    (array_agg(CASE WHEN metric_category = 'viewer_play' THEN tendency_list END ORDER BY create_time DESC) FILTER (WHERE metric_category = 'viewer_play' AND tendency_list IS NOT NULL))[1] as viewer_play_tendency_list,
    MAX(CASE WHEN metric_category = 'fan_play' THEN amount END) as fan_play_amount,
    MAX(CASE WHEN metric_category = 'fan_play' THEN amount_pass_per END) as fan_play_amount_pass_per,
    MAX(CASE WHEN metric_category = 'fan_play' THEN amount_last END) as fan_play_amount_last,
    MAX(CASE WHEN metric_category = 'fan_play' THEN amount_last_pass_per END) as fan_play_amount_last_pass_per,
    MAX(CASE WHEN metric_category = 'fan_play' THEN amount_change END) as fan_play_amount_change,
    MAX(CASE WHEN metric_category = 'fan_play' THEN amount_med END) as fan_play_amount_med,
    MAX(CASE WHEN metric_category = 'fan_play' THEN date_value END) as fan_play_date,
    (array_agg(CASE WHEN metric_category = 'fan_play' THEN tendency_list END ORDER BY create_time DESC) FILTER (WHERE metric_category = 'fan_play' AND tendency_list IS NOT NULL))[1] as fan_play_tendency_list,
    MAX(CASE WHEN metric_category = 'interact_fans_per' THEN amount END) as interact_fans_per_amount,
    MAX(CASE WHEN metric_category = 'interact_fans_per' THEN amount_pass_per END) as interact_fans_per_amount_pass_per,
    MAX(CASE WHEN metric_category = 'interact_fans_per' THEN amount_last END) as interact_fans_per_amount_last,
    MAX(CASE WHEN metric_category = 'interact_fans_per' THEN amount_last_pass_per END) as interact_fans_per_amount_last_pass_per,
    MAX(CASE WHEN metric_category = 'interact_fans_per' THEN amount_change END) as interact_fans_per_amount_change,
    MAX(CASE WHEN metric_category = 'interact_fans_per' THEN amount_med END) as interact_fans_per_amount_med,
    MAX(CASE WHEN metric_category = 'interact_fans_per' THEN date_value END) as interact_fans_per_date,
    (array_agg(CASE WHEN metric_category = 'interact_fans_per' THEN tendency_list END ORDER BY create_time DESC) FILTER (WHERE metric_category = 'interact_fans_per' AND tendency_list IS NOT NULL))[1] as interact_fans_per_tendency_list,
    MAX(CASE WHEN metric_category = 'all_play' THEN additional_data->>'tip' END) as tip,
    MAX(create_time) as create_time,
    MAX(update_time) as update_time
FROM weekly_metrics_table
WHERE report_type = 'play_analyze'
GROUP BY uid, date_value;
"""

# Legacy insert statements - these will be replaced by helper functions
insert_weekly_key_data_overview_table_sql = insert_weekly_metrics_table_sql
insert_weekly_play_analyze_table_sql = insert_weekly_metrics_table_sql
insert_weekly_attention_analyze_table_sql = insert_weekly_metrics_table_sql

create_weekly_attention_analyze_table_sql = """CREATE OR REPLACE VIEW weekly_attention_analyze_table AS
SELECT
    ROW_NUMBER() OVER (ORDER BY uid, date_value) as id,
    uid,
    MAX(CASE WHEN metric_category = 'net_attention_cnt' THEN amount END) as net_attention_cnt_amount,
    MAX(CASE WHEN metric_category = 'net_attention_cnt' THEN amount_pass_per END) as net_attention_cnt_amount_pass_per,
    MAX(CASE WHEN metric_category = 'net_attention_cnt' THEN amount_last END) as net_attention_cnt_amount_last,
    MAX(CASE WHEN metric_category = 'net_attention_cnt' THEN amount_last_pass_per END) as net_attention_cnt_amount_last_pass_per,
    MAX(CASE WHEN metric_category = 'net_attention_cnt' THEN amount_change END) as net_attention_cnt_amount_change,
    MAX(CASE WHEN metric_category = 'net_attention_cnt' THEN amount_med END) as net_attention_cnt_amount_med,
    MAX(CASE WHEN metric_category = 'net_attention_cnt' THEN date_value END) as net_attention_cnt_date,
    (array_agg(CASE WHEN metric_category = 'net_attention_cnt' THEN tendency_list END ORDER BY create_time DESC) FILTER (WHERE metric_category = 'net_attention_cnt' AND tendency_list IS NOT NULL))[1] as net_attention_cnt_tendency_list,
    MAX(CASE WHEN metric_category = 'new_attention_cnt' THEN amount END) as new_attention_cnt_amount,
    MAX(CASE WHEN metric_category = 'new_attention_cnt' THEN amount_pass_per END) as new_attention_cnt_amount_pass_per,
    MAX(CASE WHEN metric_category = 'new_attention_cnt' THEN amount_last END) as new_attention_cnt_amount_last,
    MAX(CASE WHEN metric_category = 'new_attention_cnt' THEN amount_last_pass_per END) as new_attention_cnt_amount_last_pass_per,
    MAX(CASE WHEN metric_category = 'new_attention_cnt' THEN amount_change END) as new_attention_cnt_amount_change,
    MAX(CASE WHEN metric_category = 'new_attention_cnt' THEN amount_med END) as new_attention_cnt_amount_med,
    MAX(CASE WHEN metric_category = 'new_attention_cnt' THEN date_value END) as new_attention_cnt_date,
    (array_agg(CASE WHEN metric_category = 'new_attention_cnt' THEN tendency_list END ORDER BY create_time DESC) FILTER (WHERE metric_category = 'new_attention_cnt' AND tendency_list IS NOT NULL))[1] as new_attention_cnt_tendency_list,
    MAX(CASE WHEN metric_category = 'un_attention_cnt' THEN amount END) as un_attention_cnt_amount,
    MAX(CASE WHEN metric_category = 'un_attention_cnt' THEN amount_pass_per END) as un_attention_cnt_amount_pass_per,
    MAX(CASE WHEN metric_category = 'un_attention_cnt' THEN amount_last END) as un_attention_cnt_amount_last,
    MAX(CASE WHEN metric_category = 'un_attention_cnt' THEN amount_last_pass_per END) as un_attention_cnt_amount_last_pass_per,
    MAX(CASE WHEN metric_category = 'un_attention_cnt' THEN amount_change END) as un_attention_cnt_amount_change,
    MAX(CASE WHEN metric_category = 'un_attention_cnt' THEN amount_med END) as un_attention_cnt_amount_med,
    MAX(CASE WHEN metric_category = 'un_attention_cnt' THEN date_value END) as un_attention_cnt_date,
    (array_agg(CASE WHEN metric_category = 'un_attention_cnt' THEN tendency_list END ORDER BY create_time DESC) FILTER (WHERE metric_category = 'un_attention_cnt' AND tendency_list IS NOT NULL))[1] as un_attention_cnt_tendency_list,
    MAX(CASE WHEN metric_category = 'play_trans_fan_per' THEN amount END) as play_trans_fan_per_amount,
    MAX(CASE WHEN metric_category = 'play_trans_fan_per' THEN amount_pass_per END) as play_trans_fan_per_amount_pass_per,
    MAX(CASE WHEN metric_category = 'play_trans_fan_per' THEN amount_last END) as play_trans_fan_per_amount_last,
    MAX(CASE WHEN metric_category = 'play_trans_fan_per' THEN amount_last_pass_per END) as play_trans_fan_per_amount_last_pass_per,
    MAX(CASE WHEN metric_category = 'play_trans_fan_per' THEN amount_change END) as play_trans_fan_per_amount_change,
    MAX(CASE WHEN metric_category = 'play_trans_fan_per' THEN amount_med END) as play_trans_fan_per_amount_med,
    MAX(CASE WHEN metric_category = 'play_trans_fan_per' THEN date_value END) as play_trans_fan_per_date,
    (array_agg(CASE WHEN metric_category = 'play_trans_fan_per' THEN tendency_list END ORDER BY create_time DESC) FILTER (WHERE metric_category = 'play_trans_fan_per' AND tendency_list IS NOT NULL))[1] as play_trans_fan_per_tendency_list,
    MAX(CASE WHEN metric_category = 'viewer_play' THEN amount END) as viewer_play_amount,
    MAX(CASE WHEN metric_category = 'viewer_play' THEN amount_pass_per END) as viewer_play_amount_pass_per,
    MAX(CASE WHEN metric_category = 'viewer_play' THEN amount_last END) as viewer_play_amount_last,
    MAX(CASE WHEN metric_category = 'viewer_play' THEN amount_last_pass_per END) as viewer_play_amount_last_pass_per,
    MAX(CASE WHEN metric_category = 'viewer_play' THEN amount_change END) as viewer_play_amount_change,
    MAX(CASE WHEN metric_category = 'viewer_play' THEN amount_med END) as viewer_play_amount_med,
    MAX(CASE WHEN metric_category = 'viewer_play' THEN date_value END) as viewer_play_date,
    (array_agg(CASE WHEN metric_category = 'viewer_play' THEN tendency_list END ORDER BY create_time DESC) FILTER (WHERE metric_category = 'viewer_play' AND tendency_list IS NOT NULL))[1] as viewer_play_tendency_list,
    MAX(CASE WHEN metric_category = 'arch_fans_num_all' THEN amount END) as arch_fans_num_all_amount,
    MAX(CASE WHEN metric_category = 'arch_fans_num_all' THEN amount_pass_per END) as arch_fans_num_all_amount_pass_per,
    MAX(CASE WHEN metric_category = 'arch_fans_num_all' THEN amount_last END) as arch_fans_num_all_amount_last,
    MAX(CASE WHEN metric_category = 'arch_fans_num_all' THEN amount_last_pass_per END) as arch_fans_num_all_amount_last_pass_per,
    MAX(CASE WHEN metric_category = 'arch_fans_num_all' THEN amount_change END) as arch_fans_num_all_amount_change,
    MAX(CASE WHEN metric_category = 'arch_fans_num_all' THEN amount_med END) as arch_fans_num_all_amount_med,
    MAX(CASE WHEN metric_category = 'arch_fans_num_all' THEN date_value END) as arch_fans_num_all_date,
    (array_agg(CASE WHEN metric_category = 'arch_fans_num_all' THEN tendency_list END ORDER BY create_time DESC) FILTER (WHERE metric_category = 'arch_fans_num_all' AND tendency_list IS NOT NULL))[1] as arch_fans_num_all_tendency_list,
    MAX(CASE WHEN metric_category = 'net_attention_cnt' THEN additional_data->>'tip' END) as tip,
    MAX(create_time) as create_time,
    MAX(update_time) as update_time
FROM weekly_metrics_table
WHERE report_type = 'attention_analyze'
GROUP BY uid, date_value;
"""



# --- weekly_archive_analyze_table ---
create_weekly_archive_analyze_table_sql = """CREATE TABLE IF NOT EXISTS weekly_archive_analyze_table (
    id serial4 PRIMARY KEY,
    uid varchar(50) NOT NULL,
    pub_arc_list jsonb,
    create_time bigint,
    update_time timestamp,
    UNIQUE (uid, create_time)
);"""

insert_weekly_archive_analyze_table_sql = """INSERT INTO weekly_archive_analyze_table (
    uid, pub_arc_list, create_time, update_time
) VALUES (
    $1, $2, $3, $4
) ON CONFLICT (uid, create_time) DO UPDATE SET
    pub_arc_list = EXCLUDED.pub_arc_list,
    update_time = EXCLUDED.update_time;
"""

# --- key_elec_num_table ---
create_key_elec_num_table_sql = """CREATE TABLE IF NOT EXISTS key_elec_num_table (
    id serial4 PRIMARY KEY,
    uid varchar(50) NOT NULL,
    all_total bigint,
    month_total bigint,
    custom_total bigint,
    month_switch boolean,
    create_time bigint,
    update_time timestamp,
    UNIQUE (uid, create_time)
);"""

insert_key_elec_num_table_sql = """INSERT INTO key_elec_num_table (
    uid, all_total, month_total, custom_total, month_switch, create_time, update_time
) VALUES (
    $1, $2, $3, $4, $5, $6, $7
) ON CONFLICT (uid, create_time) DO UPDATE SET
    all_total = EXCLUDED.all_total,
    month_total = EXCLUDED.month_total,
    custom_total = EXCLUDED.custom_total,
    month_switch = EXCLUDED.month_switch,
    update_time = EXCLUDED.update_time;
"""

# --- key_video_compare_table ---
create_key_video_compare_table_sql = """CREATE TABLE IF NOT EXISTS key_video_compare_table (
    id serial4 PRIMARY KEY,
    uid varchar(50) NOT NULL,
    aid varchar(50),
    bvid varchar(50),
    title text,
    cover text,
    pubtime bigint,
    duration bigint,
    is_only_self boolean,
    -- Main statistics
    play bigint,
    vt bigint,
    like_count bigint,
    comment bigint,
    dm bigint,
    fav bigint,
    coin bigint,
    share bigint,
    unfollow bigint,
    -- Performance metrics
    full_play_ratio bigint,
    play_viewer_rate bigint,
    play_fan_rate bigint,
    active_fans_rate bigint,
    tm_rate bigint,
    tm_pass_rate bigint,
    crash_rate bigint,
    interact_rate bigint,
    avg_play_time bigint,
    total_new_attention_cnt bigint,
    play_trans_fan_rate bigint,
    tm_star bigint,
    -- P50 metrics
    crash_p50 bigint,
    crash_viewer_p50 bigint,
    crash_fan_p50 bigint,
    interact_p50 bigint,
    interact_viewer_p50 bigint,
    interact_fan_p50 bigint,
    play_trans_fan_p50 bigint,
    -- Hour statistics
    hour_play bigint,
    hour_vt bigint,
    hour_like bigint,
    hour_comment bigint,
    hour_dm bigint,
    hour_fav bigint,
    hour_coin bigint,
    hour_share bigint,
    hour_tm_pass_rate bigint,
    hour_interact_rate bigint,
    hour_tm_star bigint,
    create_time bigint,
    update_time timestamp,
    UNIQUE (uid, bvid, create_time)
);"""

insert_key_video_compare_table_sql = """INSERT INTO key_video_compare_table (
    uid, aid, bvid, title, cover, pubtime, duration, is_only_self,
    play, vt, like_count, comment, dm, fav, coin, share, unfollow,
    full_play_ratio, play_viewer_rate, play_fan_rate, active_fans_rate,
    tm_rate, tm_pass_rate, crash_rate, interact_rate, avg_play_time,
    total_new_attention_cnt, play_trans_fan_rate, tm_star,
    crash_p50, crash_viewer_p50, crash_fan_p50, interact_p50,
    interact_viewer_p50, interact_fan_p50, play_trans_fan_p50,
    hour_play, hour_vt, hour_like, hour_comment, hour_dm, hour_fav,
    hour_coin, hour_share, hour_tm_pass_rate, hour_interact_rate, hour_tm_star,
    create_time, update_time
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17,
    $18, $19, $20, $21, $22, $23, $24, $25, $26, $27, $28, $29, $30, $31, $32,
    $33, $34, $35, $36, $37, $38, $39, $40, $41, $42, $43, $44, $45, $46, $47, $48
) ON CONFLICT (uid, bvid, create_time) DO UPDATE SET
    aid = EXCLUDED.aid,
    title = EXCLUDED.title,
    cover = EXCLUDED.cover,
    pubtime = EXCLUDED.pubtime,
    duration = EXCLUDED.duration,
    is_only_self = EXCLUDED.is_only_self,
    play = EXCLUDED.play,
    vt = EXCLUDED.vt,
    like_count = EXCLUDED.like_count,
    comment = EXCLUDED.comment,
    dm = EXCLUDED.dm,
    fav = EXCLUDED.fav,
    coin = EXCLUDED.coin,
    share = EXCLUDED.share,
    unfollow = EXCLUDED.unfollow,
    full_play_ratio = EXCLUDED.full_play_ratio,
    play_viewer_rate = EXCLUDED.play_viewer_rate,
    play_fan_rate = EXCLUDED.play_fan_rate,
    active_fans_rate = EXCLUDED.active_fans_rate,
    tm_rate = EXCLUDED.tm_rate,
    tm_pass_rate = EXCLUDED.tm_pass_rate,
    crash_rate = EXCLUDED.crash_rate,
    interact_rate = EXCLUDED.interact_rate,
    avg_play_time = EXCLUDED.avg_play_time,
    total_new_attention_cnt = EXCLUDED.total_new_attention_cnt,
    play_trans_fan_rate = EXCLUDED.play_trans_fan_rate,
    tm_star = EXCLUDED.tm_star,
    crash_p50 = EXCLUDED.crash_p50,
    crash_viewer_p50 = EXCLUDED.crash_viewer_p50,
    crash_fan_p50 = EXCLUDED.crash_fan_p50,
    interact_p50 = EXCLUDED.interact_p50,
    interact_viewer_p50 = EXCLUDED.interact_viewer_p50,
    interact_fan_p50 = EXCLUDED.interact_fan_p50,
    play_trans_fan_p50 = EXCLUDED.play_trans_fan_p50,
    hour_play = EXCLUDED.hour_play,
    hour_vt = EXCLUDED.hour_vt,
    hour_like = EXCLUDED.hour_like,
    hour_comment = EXCLUDED.hour_comment,
    hour_dm = EXCLUDED.hour_dm,
    hour_fav = EXCLUDED.hour_fav,
    hour_coin = EXCLUDED.hour_coin,
    hour_share = EXCLUDED.hour_share,
    hour_tm_pass_rate = EXCLUDED.hour_tm_pass_rate,
    hour_interact_rate = EXCLUDED.hour_interact_rate,
    hour_tm_star = EXCLUDED.hour_tm_star,
    update_time = EXCLUDED.update_time;
"""

# --- key_video_archive_table ---
create_key_video_archive_table_sql = """CREATE TABLE IF NOT EXISTS key_video_archive_table (
    id serial4 PRIMARY KEY,
    uid varchar(50) NOT NULL,
    aid varchar(50),
    bvid varchar(50),
    title text,
    cover text,
    ctime bigint,
    pubtime bigint,
    duration bigint,
    play_num bigint,
    reply_num bigint,
    likes_num bigint,
    vt_num bigint,
    fans_data text,
    full_play_ratio text,
    is_only_self boolean,
    create_time bigint,
    update_time timestamp,
    UNIQUE (uid, bvid)
);"""

insert_key_video_archive_table_sql = """INSERT INTO key_video_archive_table (
    uid, aid, bvid, title, cover, ctime, pubtime, duration, play_num, reply_num,
    likes_num, vt_num, fans_data, full_play_ratio, is_only_self, create_time, update_time
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17
) ON CONFLICT (uid, bvid) DO UPDATE SET
    aid = EXCLUDED.aid,
    title = EXCLUDED.title,
    cover = EXCLUDED.cover,
    ctime = EXCLUDED.ctime,
    pubtime = EXCLUDED.pubtime,
    duration = EXCLUDED.duration,
    play_num = EXCLUDED.play_num,
    reply_num = EXCLUDED.reply_num,
    likes_num = EXCLUDED.likes_num,
    vt_num = EXCLUDED.vt_num,
    fans_data = EXCLUDED.fans_data,
    full_play_ratio = EXCLUDED.full_play_ratio,
    is_only_self = EXCLUDED.is_only_self,
    update_time = EXCLUDED.update_time;
"""
